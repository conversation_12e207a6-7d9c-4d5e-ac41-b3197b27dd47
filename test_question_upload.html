<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Upload Test - Kazakh Lingo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .file-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-row .form-group {
            flex: 1;
        }
        .json-example {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Question Upload Test - Kazakh Lingo</h1>
        <p>Тестирование API создания вопросов с загрузкой файлов (изображения и аудио)</p>

        <form id="questionForm">
            <div class="form-group">
                <label for="questionType">Тип вопроса *</label>
                <select id="questionType" required>
                    <option value="">Выберите тип вопроса</option>
                    <option value="build-sentence">Build Sentence</option>
                    <option value="kz-word-to-ru-word">Kazakh Word to Russian Word</option>
                    <option value="by-letter">By Letter</option>
                    <option value="multiple-choice">Multiple Choice</option>
                </select>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="kazPlaintext">Казахский текст *</label>
                    <input type="text" id="kazPlaintext" placeholder="Сәлем" required>
                </div>
                <div class="form-group">
                    <label for="rusPlaintext">Русский текст *</label>
                    <input type="text" id="rusPlaintext" placeholder="Привет" required>
                </div>
            </div>

            <div class="form-group">
                <label for="sequence">Последовательность (JSON массив)</label>
                <input type="text" id="sequence" placeholder='[1, 2, 3]'>
                <div class="file-info">Опционально: массив ID слов для правильного ответа</div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="imageFile">Изображение (опционально)</label>
                    <input type="file" id="imageFile" accept=".jpg,.jpeg,.png,.gif,.webp">
                    <div class="file-info">Поддерживаемые форматы: JPG, PNG, GIF, WebP (макс. 5MB)</div>
                </div>
                <div class="form-group">
                    <label for="audioFile">Аудио (опционально)</label>
                    <input type="file" id="audioFile" accept=".mp3,.wav,.ogg,.m4a">
                    <div class="file-info">Поддерживаемые форматы: MP3, WAV, OGG, M4A (макс. 10MB)</div>
                </div>
            </div>

            <div class="form-group">
                <button type="submit">Создать вопрос с файлами</button>
                <button type="button" onclick="createJSONQuestion()">Создать JSON вопрос</button>
                <button type="button" onclick="clearForm()">Очистить форму</button>
            </div>
        </form>

        <div class="json-example">
            <strong>Пример JSON для question_data:</strong><br>
            {<br>
            &nbsp;&nbsp;"type": "build-sentence",<br>
            &nbsp;&nbsp;"correct_answer": {<br>
            &nbsp;&nbsp;&nbsp;&nbsp;"kaz_plaintext": "Сәлем",<br>
            &nbsp;&nbsp;&nbsp;&nbsp;"rus_plaintext": "Привет",<br>
            &nbsp;&nbsp;&nbsp;&nbsp;"sequence": [1, 2]<br>
            &nbsp;&nbsp;}<br>
            }
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/v1';

        document.getElementById('questionForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await createQuestionWithFiles();
        });

        async function createQuestionWithFiles() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Создание вопроса...';

            try {
                // Собираем данные формы
                const questionData = {
                    type: document.getElementById('questionType').value,
                    correct_answer: {
                        kaz_plaintext: document.getElementById('kazPlaintext').value,
                        rus_plaintext: document.getElementById('rusPlaintext').value
                    }
                };

                // Добавляем sequence если указан
                const sequenceValue = document.getElementById('sequence').value.trim();
                if (sequenceValue) {
                    try {
                        questionData.correct_answer.sequence = JSON.parse(sequenceValue);
                    } catch (e) {
                        throw new Error('Неверный формат JSON для последовательности');
                    }
                }

                // Создаем FormData
                const formData = new FormData();
                formData.append('question_data', JSON.stringify(questionData));

                // Добавляем файлы если выбраны
                const imageFile = document.getElementById('imageFile').files[0];
                if (imageFile) {
                    formData.append('image', imageFile);
                }

                const audioFile = document.getElementById('audioFile').files[0];
                if (audioFile) {
                    formData.append('audio', audioFile);
                }

                // Отправляем запрос
                const response = await fetch(`${API_BASE}/questions`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Вопрос создан успешно!\n\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Ошибка: ${result.error || 'Неизвестная ошибка'}\n\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Ошибка: ${error.message}`;
            }
        }

        async function createJSONQuestion() {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = 'Создание JSON вопроса...';

            try {
                const questionData = {
                    type: document.getElementById('questionType').value,
                    correct_answer: {
                        kaz_plaintext: document.getElementById('kazPlaintext').value,
                        rus_plaintext: document.getElementById('rusPlaintext').value
                    }
                };

                const sequenceValue = document.getElementById('sequence').value.trim();
                if (sequenceValue) {
                    try {
                        questionData.correct_answer.sequence = JSON.parse(sequenceValue);
                    } catch (e) {
                        throw new Error('Неверный формат JSON для последовательности');
                    }
                }

                const response = await fetch(`${API_BASE}/questions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(questionData)
                });

                const result = await response.json();

                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ JSON вопрос создан успешно!\n\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Ошибка: ${result.error || 'Неизвестная ошибка'}\n\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Ошибка: ${error.message}`;
            }
        }

        function clearForm() {
            document.getElementById('questionForm').reset();
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'none';
        }

        // Предзаполнение формы для тестирования
        document.getElementById('questionType').value = 'build-sentence';
        document.getElementById('kazPlaintext').value = 'Сәлем';
        document.getElementById('rusPlaintext').value = 'Привет';
        document.getElementById('sequence').value = '[1, 2]';
    </script>
</body>
</html>
