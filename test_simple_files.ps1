# Simple test for question creation with files

Write-Host "Testing Question Creation with Files API..." -ForegroundColor Green

# Create test files
$testAudioContent = "This is a test audio file for question"
$testAudioPath = "test_question_audio.mp3"
[System.IO.File]::WriteAllText($testAudioPath, $testAudioContent)

$testImageContent = "This is a test image file for question"
$testImagePath = "test_question_image.jpg"
[System.IO.File]::WriteAllText($testImagePath, $testImageContent)

Write-Host "Created test files: $testAudioPath, $testImagePath" -ForegroundColor Yellow

# Simple JSON without complex escaping
$questionJson = '{"type":"multiple-choice","correct_answer":{"kaz_plaintext":"Мектеп","rus_plaintext":"Школа","sequence":[1,2,3]}}'

Write-Host "Question JSON: $questionJson" -ForegroundColor Cyan

# Test with curl
Write-Host "`nTesting question creation with files using curl..." -ForegroundColor Yellow
try {
    $curlResult = & curl.exe -X POST `
        -F "question_data=$questionJson" `
        -F "audio=@$testAudioPath" `
        -F "image=@$testImagePath" `
        http://localhost:8080/v1/questions
    
    Write-Host "✅ Curl result:" -ForegroundColor Green
    Write-Host $curlResult -ForegroundColor Cyan
} catch {
    Write-Host "❌ Curl failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test only JSON (without files)
Write-Host "`nTesting JSON only..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/v1/questions" -Method POST -ContentType "application/json" -Body $questionJson
    Write-Host "✅ JSON only successful!" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Cyan
} catch {
    Write-Host "❌ JSON only failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Write-Host "`nCleaning up test files..." -ForegroundColor Yellow
Remove-Item $testAudioPath -ErrorAction SilentlyContinue
Remove-Item $testImagePath -ErrorAction SilentlyContinue
Write-Host "Test completed!" -ForegroundColor Green
