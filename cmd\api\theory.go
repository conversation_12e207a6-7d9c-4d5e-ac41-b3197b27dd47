package main

import (
	"encoding/json"
	"errors"
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"net/http"
	"strconv"
)

// Создание новой теории
func (app *application) createTheoryHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Theory
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Theories.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"theory": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// Получение теории по ID
func (app *application) showTheoryHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	theory, err := app.models.Theories.Get(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"theory": theory}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// Получение всех теорий
func (app *application) listTheoriesHandler(w http.ResponseWriter, r *http.Request) {
	theories, err := app.models.Theories.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"theories": theories}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// Обновление теории
func (app *application) updateTheoryHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	var input data.Theory
	err = json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	input.ID = id
	err = app.models.Theories.Update(&input)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"theory": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// Удаление теории
func (app *application) deleteTheoryHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	err = app.models.Theories.Delete(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"message": "theory successfully deleted"}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}
