# Test JSON question creation

Write-Host "Testing Question Creation API..." -ForegroundColor Green

# Test JSON question creation
$questionData = @{
    type = "build-sentence"
    correct_answer = @{
        kaz_plaintext = "Сәлем"
        rus_plaintext = "Привет"
        sequence = @(1, 2)
    }
} | ConvertTo-Json -Depth 3

Write-Host "Question data:" -ForegroundColor Yellow
Write-Host $questionData -ForegroundColor Cyan

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/v1/questions" -Method POST -ContentType "application/json" -Body $questionData
    Write-Host "✅ JSON Question created successfully!" -ForegroundColor Green
    Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor Cyan
} catch {
    Write-Host "❌ Error creating JSON question: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody" -ForegroundColor Red
    }
}
