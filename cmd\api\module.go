package main

import (
	"encoding/json"
	"errors"
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"net/http"
	"strconv"
)

// createModuleHandler создает новый модуль
func (app *application) createModuleHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Module
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Modules.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"module": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// showModuleHandler получает модуль по ID
func (app *application) showModuleHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	module, err := app.models.Modules.Get(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"module": module}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// listModulesHandler получает все модули
func (app *application) listModulesHandler(w http.ResponseWriter, r *http.Request) {
	modules, err := app.models.Modules.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"modules": modules}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// updateModuleHandler обновляет модуль
func (app *application) updateModuleHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	var input data.Module
	err = json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	input.ID = id
	err = app.models.Modules.Update(&input)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"module": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// deleteModuleHandler удаляет модуль
func (app *application) deleteModuleHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	err = app.models.Modules.Delete(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"message": "module successfully deleted"}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

// getFullModuleHandler возвращает полный модуль с теориями и вопросами
func (app *application) getFullModuleHandler(w http.ResponseWriter, r *http.Request) {
	// Получаем ID модуля из запроса
	idParam := r.URL.Query().Get("id")
	if idParam == "" {
		app.badRequestResponse(w, r, errors.New("missing module ID"))
		return
	}

	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || id < 1 {
		app.badRequestResponse(w, r, errors.New("invalid module ID"))
		return
	}

	// Получаем полный модуль с теориями и вопросами
	module, err := app.models.Modules.GetFullModuleById(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	// Отправляем ответ
	err = app.writeJSON(w, http.StatusOK, envelope{"module": module}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

func (app *application) getUserPassedModulesHandler(w http.ResponseWriter, r *http.Request) {
	id, err := app.readIDParam(r)
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
	}

	passedModules, err := app.models.Modules.GetUserPassedModules(id)
	if err != nil {
		if errors.Is(err, data.ErrRecordNotFound) {
			app.notFoundResponse(w, r)
			return
		}
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"passed_modules": passedModules}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}
