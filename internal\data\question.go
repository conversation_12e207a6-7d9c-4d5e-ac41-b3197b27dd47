package data

import (
	"database/sql"
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v8"
)

type CorrectAnswer struct {
	KazPlaintext string `json:"kaz_plaintext"`
	RusPlaintext string `json:"rus_plaintext"`
	ImageUrl     string `json:"image_url"` // тут
	AudioUrl     string `json:"audio_url"` // тут
	Sequence     []int  `json:"sequence"`
}

type Question struct {
	ID            int           `json:"id"`
	Type          string        `json:"type"`
	Words         []Word        `json:"words"`
	CorrectAnswer CorrectAnswer `json:"correct_answer"`
	ImageURL      string        `json:"image_url"`
	CreatedAt     time.Time     `json:"created_at"`
}

type QuestionModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

// Insert Создание вопроса и добавление связей со словами
func (m *QuestionModel) Insert(question *Question) error {
	query := `
		INSERT INTO questions (type, correct_answer, image_url)
		VALUES ($1, $2, $3)
		RETURNING id, created_at
	`
	correctAnswerJSON, _ := json.Marshal(question.CorrectAnswer)
	args := []any{question.Type, correctAnswerJSON, question.ImageURL}

	err := m.DB.QueryRow(query, args...).Scan(&question.ID, &question.CreatedAt)
	if err != nil {
		return err
	}

	// Вставка связей в question_words
	for i, word := range question.Words {
		_, err := m.DB.Exec(
			`INSERT INTO question_words (question_id, word_id, sequence_order) VALUES ($1, $2, $3)`,
			question.ID, word.ID, i+1,
		)
		if err != nil {
			return err
		}
	}
	return nil
}

// Get Получение вопроса с его словами
func (m *QuestionModel) Get(id int) (*Question, error) {
	query := `
		SELECT q.id, q.type, q.correct_answer, q.image_url, w.id, w.kaz_plaintext, w.rus_plaintext, w.audio_url, qw.sequence_order
		FROM questions q
		JOIN question_words qw ON q.id = qw.question_id
		JOIN words w ON qw.word_id = w.id
		WHERE q.id = $1
		ORDER BY qw.sequence_order
	`
	rows, err := m.DB.Query(query, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var question Question
	var correctAnswerJSON []byte
	var words []Word

	for rows.Next() {
		var word Word
		var sequenceOrder int

		if err := rows.Scan(&question.ID, &question.Type, &correctAnswerJSON, &question.ImageURL, &word.ID, &word.KazPlaintext, &word.RusPlaintext, &word.AudioURL, &sequenceOrder); err != nil {
			return nil, err
		}
		words = append(words, word)
	}

	json.Unmarshal(correctAnswerJSON, &question.CorrectAnswer)
	question.Words = words
	return &question, nil
}

// GetAll получает список всех вопросов с их словами
func (m *QuestionModel) GetAll() ([]*Question, error) {
	query := `
		SELECT q.id, q.type, q.correct_answer, q.image_url, w.id, w.kaz_plaintext, w.rus_plaintext, w.audio_url, qw.sequence_order
		FROM questions q
		JOIN question_words qw ON q.id = qw.question_id
		JOIN words w ON qw.word_id = w.id
		ORDER BY q.id, qw.sequence_order
	`

	rows, err := m.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var questions []*Question
	questionsMap := make(map[int]*Question)

	for rows.Next() {
		var questionID int
		var questionType, imageURL string
		var correctAnswerJSON []byte
		var word Word
		var sequenceOrder int

		if err := rows.Scan(&questionID, &questionType, &correctAnswerJSON, &imageURL, &word.ID, &word.KazPlaintext, &word.RusPlaintext, &word.AudioURL, &sequenceOrder); err != nil {
			return nil, err
		}

		// Проверяем, существует ли вопрос с таким ID
		if _, exists := questionsMap[questionID]; !exists {
			questionsMap[questionID] = &Question{
				ID:       questionID,
				Type:     questionType,
				ImageURL: imageURL,
				Words:    []Word{},
			}
			// Декодируем JSON с правильным ответом
			json.Unmarshal(correctAnswerJSON, &questionsMap[questionID].CorrectAnswer)

			// Добавляем новый вопрос в список вопросов
			questions = append(questions, questionsMap[questionID])
		}

		// Добавляем слово к существующему вопросу
		questionsMap[questionID].Words = append(questionsMap[questionID].Words, word)
	}

	if err = rows.Err(); err != nil {
		return nil, err
	}

	return questions, nil
}
