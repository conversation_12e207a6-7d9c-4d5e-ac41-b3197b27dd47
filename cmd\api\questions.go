package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/olzzhas/kazakh-lingo/internal/data"
)

func (app *application) createQuestionHandler(w http.ResponseWriter, r *http.Request) {
	// Логируем начало обработки запроса
	app.logger.PrintInfo("creating new question", map[string]any{
		"method": r.Method,
		"url":    r.URL.String(),
	}, "questions")

	// Проверяем Content-Type
	contentType := r.Header.Get("Content-Type")
	if strings.Contains(contentType, "multipart/form-data") {
		app.createQuestionWithFilesHandler(w, r)
		return
	}

	// Обработка JSON запроса (старый способ)
	var input data.Question
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "decode_json",
		}, "questions")
		app.badRequestResponse(w, r, err)
		return
	}

	// Валидация обязательных полей
	if err := app.validateQuestion(&input); err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "validate_question",
		}, "questions")
		app.badRequestResponse(w, r, err)
		return
	}

	// Вставляем вопрос
	err = app.models.Questions.Insert(&input)
	if err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "insert_question",
		}, "questions")
		app.serverErrorResponse(w, r, err)
		return
	}

	app.logger.PrintInfo("question created successfully", map[string]any{
		"question_id": fmt.Sprintf("%d", input.ID),
		"type":        input.Type,
	}, "questions")

	err = app.writeJSON(w, http.StatusCreated, envelope{"question": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) listQuestionsHandler(w http.ResponseWriter, r *http.Request) {
	questions, err := app.models.Questions.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"question": questions}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) showQuestionHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	question, err := app.models.Questions.Get(id)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"question": question}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) createWordHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Word
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Words.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"word": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) listWordsHandler(w http.ResponseWriter, r *http.Request) {
	words, err := app.models.Words.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"words": words}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) createSentenceHandler(w http.ResponseWriter, r *http.Request) {
	var input data.Sentence
	err := json.NewDecoder(r.Body).Decode(&input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}
	err = app.models.Sentences.Insert(&input)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
	err = app.writeJSON(w, http.StatusOK, envelope{"sentence": input}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) showSentenceHandler(w http.ResponseWriter, r *http.Request) {
	id, err := strconv.Atoi(r.URL.Query().Get("id"))
	if err != nil || id < 1 {
		app.notFoundResponse(w, r)
		return
	}

	sentence, err := app.models.Sentences.Get(id)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"sentence": sentence}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

}

func (app *application) listSentencesHandler(w http.ResponseWriter, r *http.Request) {
	sentences, err := app.models.Sentences.GetAll()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"sentences": sentences}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
	}
}

// validateQuestion валидирует данные вопроса
func (app *application) validateQuestion(question *data.Question) error {
	if question.Type == "" {
		return fmt.Errorf("question type is required")
	}

	// Проверяем допустимые типы вопросов
	validTypes := []string{"build-sentence", "kz-word-to-ru-word", "by-letter", "multiple-choice"}
	isValidType := false
	for _, validType := range validTypes {
		if question.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return fmt.Errorf("invalid question type: %s. Allowed types: %s", question.Type, strings.Join(validTypes, ", "))
	}

	// Проверяем наличие правильного ответа
	if question.CorrectAnswer.KazPlaintext == "" && question.CorrectAnswer.RusPlaintext == "" {
		return fmt.Errorf("correct answer must have at least kaz_plaintext or rus_plaintext")
	}

	return nil
}

// createQuestionWithFilesHandler обрабатывает создание вопроса с файлами
func (app *application) createQuestionWithFilesHandler(w http.ResponseWriter, r *http.Request) {
	// Ограничиваем размер запроса до 20MB (для изображения + аудио + данные)
	err := r.ParseMultipartForm(20 << 20)
	if err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "parse_multipart_form",
		}, "questions")
		app.badRequestResponse(w, r, fmt.Errorf("failed to parse multipart form: %v", err))
		return
	}

	// Получаем JSON данные вопроса
	questionDataStr := r.FormValue("question_data")
	if questionDataStr == "" {
		app.badRequestResponse(w, r, fmt.Errorf("question_data field is required"))
		return
	}

	var input data.Question
	err = json.Unmarshal([]byte(questionDataStr), &input)
	if err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "unmarshal_question_data",
		}, "questions")
		app.badRequestResponse(w, r, fmt.Errorf("invalid question_data JSON: %v", err))
		return
	}

	// Валидация обязательных полей
	if err := app.validateQuestion(&input); err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "validate_question_with_files",
		}, "questions")
		app.badRequestResponse(w, r, err)
		return
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// Обработка загрузки изображения (опционально)
	imageFile, imageHeader, err := r.FormFile("image")
	if err == nil {
		defer imageFile.Close()

		// Валидация типа изображения
		if !isValidImageFile(imageHeader.Filename) {
			app.badRequestResponse(w, r, fmt.Errorf("invalid image file type. Allowed: jpg, jpeg, png, gif, webp"))
			return
		}

		app.logger.PrintInfo("uploading question image", map[string]any{
			"filename": imageHeader.Filename,
			"size":     imageHeader.Size,
		}, "questions")

		// Загружаем изображение в MinIO
		imageURL, err := app.storages.MinIO.UploadImage(ctx, imageFile, imageHeader)
		if err != nil {
			app.logger.PrintError(err, map[string]any{
				"action": "upload_question_image",
			}, "questions")
			app.serverErrorResponse(w, r, err)
			return
		}

		input.ImageURL = imageURL
		app.logger.PrintInfo("question image uploaded successfully", map[string]any{
			"image_url": imageURL,
		}, "questions")
	} else if err.Error() != "http: no such file" {
		app.logger.PrintError(err, map[string]any{
			"action": "get_image_file",
		}, "questions")
		app.badRequestResponse(w, r, fmt.Errorf("error processing image file: %v", err))
		return
	}

	// Обработка загрузки аудио (опционально)
	audioFile, audioHeader, err := r.FormFile("audio")
	if err == nil {
		defer audioFile.Close()

		// Валидация типа аудио
		if !isValidAudioFile(audioHeader.Filename) {
			app.badRequestResponse(w, r, fmt.Errorf("invalid audio file type. Allowed: mp3, wav, ogg, m4a"))
			return
		}

		app.logger.PrintInfo("uploading question audio", map[string]any{
			"filename": audioHeader.Filename,
			"size":     audioHeader.Size,
		}, "questions")

		// Загружаем аудио в MinIO
		audioURL, err := app.storages.MinIO.UploadAudio(ctx, audioFile, audioHeader)
		if err != nil {
			app.logger.PrintError(err, map[string]any{
				"action": "upload_question_audio",
			}, "questions")
			app.serverErrorResponse(w, r, err)
			return
		}

		input.CorrectAnswer.AudioUrl = audioURL
		app.logger.PrintInfo("question audio uploaded successfully", map[string]any{
			"audio_url": audioURL,
		}, "questions")
	} else if err.Error() != "http: no such file" {
		app.logger.PrintError(err, map[string]any{
			"action": "get_audio_file",
		}, "questions")
		app.badRequestResponse(w, r, fmt.Errorf("error processing audio file: %v", err))
		return
	}

	// Вставляем вопрос в базу данных
	err = app.models.Questions.Insert(&input)
	if err != nil {
		app.logger.PrintError(err, map[string]any{
			"action": "insert_question_with_files",
		}, "questions")
		app.serverErrorResponse(w, r, err)
		return
	}

	app.logger.PrintInfo("question with files created successfully", map[string]any{
		"question_id": fmt.Sprintf("%d", input.ID),
		"type":        input.Type,
		"has_image":   input.ImageURL != "",
		"has_audio":   input.CorrectAnswer.AudioUrl != "",
	}, "questions")

	err = app.writeJSON(w, http.StatusCreated, envelope{
		"question": input,
		"message":  "Question created successfully with files",
	}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}
