# Questions API Documentation

## Overview
API для создания и управления вопросами в приложении Kazakh-<PERSON>o. Поддерживает создание вопросов с загрузкой изображений и аудио файлов через MinIO.

## Base URL
```
http://localhost:8080/v1
```

## Endpoints

### 1. Create Question (JSON)
Создание вопроса с JSON данными (без файлов).

**Endpoint:** `POST /questions`  
**Content-Type:** `application/json`

#### Request Body
```json
{
  "type": "build-sentence",
  "correct_answer": {
    "kaz_plaintext": "Сәлем",
    "rus_plaintext": "Привет",
    "sequence": [1, 2]
  }
}
```

#### Response
```json
{
  "question": {
    "id": 23,
    "type": "build-sentence",
    "words": null,
    "correct_answer": {
      "kaz_plaintext": "Сәлем",
      "rus_plaintext": "Привет",
      "image_url": "",
      "audio_url": "",
      "sequence": [1, 2]
    },
    "image_url": "",
    "created_at": "2025-06-19T19:38:41.16453Z"
  }
}
```

### 2. Create Question with Files
Создание вопроса с загрузкой изображения и/или аудио файла.

**Endpoint:** `POST /questions`  
**Content-Type:** `multipart/form-data`

#### Form Fields
- `question_data` (required): JSON строка с данными вопроса
- `image` (optional): Файл изображения (JPG, PNG, GIF, WebP, макс. 5MB)
- `audio` (optional): Аудио файл (MP3, WAV, OGG, M4A, макс. 10MB)

#### Example using curl
```bash
curl -X POST \
  -F "question_data={\"type\":\"multiple-choice\",\"correct_answer\":{\"kaz_plaintext\":\"Мектеп\",\"rus_plaintext\":\"Школа\",\"sequence\":[1,2,3]}}" \
  -F "image=@question_image.jpg" \
  -F "audio=@question_audio.mp3" \
  http://localhost:8080/v1/questions
```

#### Response
```json
{
  "question": {
    "id": 25,
    "type": "multiple-choice",
    "words": null,
    "correct_answer": {
      "kaz_plaintext": "Мектеп",
      "rus_plaintext": "Школа",
      "image_url": "",
      "audio_url": "http://minio:9000/klingo-audio/uuid-audio.mp3",
      "sequence": [1, 2, 3]
    },
    "image_url": "http://minio:9000/klingo-images/uuid-image.jpg",
    "created_at": "2025-06-19T19:40:00.123456Z"
  },
  "message": "Question created successfully with files"
}
```

### 3. Get All Questions
Получение списка всех вопросов.

**Endpoint:** `GET /questions`

#### Response
```json
{
  "questions": [
    {
      "id": 23,
      "type": "build-sentence",
      "words": [
        {
          "id": 1,
          "kaz_plaintext": "Сәлем",
          "rus_plaintext": "Привет",
          "audio_url": ""
        }
      ],
      "correct_answer": {
        "kaz_plaintext": "Сәлем",
        "rus_plaintext": "Привет",
        "sequence": [1, 2]
      },
      "image_url": "",
      "created_at": "2025-06-19T19:38:41.16453Z"
    }
  ]
}
```

### 4. Get Question by ID
Получение конкретного вопроса по ID.

**Endpoint:** `GET /questions/{id}`

#### Response
```json
{
  "question": {
    "id": 23,
    "type": "build-sentence",
    "words": [
      {
        "id": 1,
        "kaz_plaintext": "Сәлем",
        "rus_plaintext": "Привет",
        "audio_url": ""
      }
    ],
    "correct_answer": {
      "kaz_plaintext": "Сәлем",
      "rus_plaintext": "Привет",
      "sequence": [1, 2]
    },
    "image_url": "",
    "created_at": "2025-06-19T19:38:41.16453Z"
  }
}
```

## Question Types
Поддерживаемые типы вопросов:

1. **build-sentence** - Построение предложения
2. **kz-word-to-ru-word** - Перевод казахского слова на русский
3. **by-letter** - Вопрос по буквам
4. **multiple-choice** - Множественный выбор

## Validation Rules

### Required Fields
- `type`: Тип вопроса (обязательно)
- `correct_answer.kaz_plaintext` или `correct_answer.rus_plaintext`: Хотя бы один из текстов (обязательно)

### File Validation
#### Images
- **Форматы:** JPG, JPEG, PNG, GIF, WebP
- **Максимальный размер:** 5MB
- **Поле формы:** `image`

#### Audio
- **Форматы:** MP3, WAV, OGG, M4A
- **Максимальный размер:** 10MB
- **Поле формы:** `audio`

## Error Responses

### 400 Bad Request
```json
{
  "error": "question type is required"
}
```

### 400 Bad Request (Invalid File)
```json
{
  "error": "invalid image file type. Allowed: jpg, jpeg, png, gif, webp"
}
```

### 500 Internal Server Error
```json
{
  "error": "internal server error"
}
```

## Features

### ✅ Implemented
- ✅ JSON создание вопросов
- ✅ Multipart создание вопросов с файлами
- ✅ Валидация типов вопросов
- ✅ Валидация файлов (тип и размер)
- ✅ Автоматическая загрузка в MinIO
- ✅ Генерация уникальных имен файлов (UUID)
- ✅ Подробное логирование
- ✅ Получение списка вопросов
- ✅ Получение вопроса по ID

### 🔄 File Storage
Файлы автоматически загружаются в MinIO:
- **Изображения:** bucket `klingo-images`
- **Аудио:** bucket `klingo-audio`
- **URL формат:** `http://minio:9000/{bucket}/{uuid}.{extension}`

## Testing

### HTML Test Interface
Откройте `test_question_upload.html` в браузере для интерактивного тестирования API.

### PowerShell Scripts
- `test_question_json.ps1` - Тестирование JSON API
- `test_english_files.ps1` - Тестирование с файлами

## Examples

### JavaScript (Browser)
```javascript
// JSON создание
const questionData = {
  type: "build-sentence",
  correct_answer: {
    kaz_plaintext: "Сәлем",
    rus_plaintext: "Привет",
    sequence: [1, 2]
  }
};

fetch('/v1/questions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(questionData)
});

// Создание с файлами
const formData = new FormData();
formData.append('question_data', JSON.stringify(questionData));
formData.append('image', imageFile);
formData.append('audio', audioFile);

fetch('/v1/questions', {
  method: 'POST',
  body: formData
});
```

### Go
```go
// Создание вопроса
question := &data.Question{
    Type: "build-sentence",
    CorrectAnswer: data.CorrectAnswer{
        KazPlaintext: "Сәлем",
        RusPlaintext: "Привет",
        Sequence:     []int{1, 2},
    },
}

err := app.models.Questions.Insert(question)
```

## Notes
- Файлы являются опциональными - можно создавать вопросы только с JSON
- При загрузке файлов URL автоматически добавляются в соответствующие поля
- Все файлы получают уникальные UUID имена для предотвращения конфликтов
- MinIO настроен для публичного доступа к файлам
