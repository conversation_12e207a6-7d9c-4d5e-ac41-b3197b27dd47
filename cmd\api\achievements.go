package main

import (
	"github.com/olzzhas/kazakh-lingo/internal/data"
	"net/http"
)

func (app *application) createAchievementHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Type        string `json:"type"` // progress, streaks, friends, etc.
		Target      int    `json:"target"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	achievement := &data.Achievement{
		Name:        input.Name,
		Description: input.Description,
		Type:        input.Type,
		Target:      input.Target,
	}

	achievement, err = app.models.Achievements.SaveAchievement(achievement)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusCreated, envelope{"achievement": achievement}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) listAchievementsHandler(w http.ResponseWriter, r *http.Request) {
	achievements, err := app.models.Achievements.GetAllAchievements()
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"achievements": achievements}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) listUserAchievementsHandler(w http.ResponseWriter, r *http.Request) {
	userID, err := app.readIDParam(r)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	achievements, err := app.models.Achievements.GetUserAchievements(userID)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"achievements": achievements}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) updateAchievementProgressHandler(w http.ResponseWriter, r *http.Request) {
	var input struct {
		UserID          int    `json:"user_id"`
		Progress        int    `json:"progress"`         // Прогресс, добавляемый за действие
		AchievementType string `json:"achievement_type"` // Тип достижений, например "lesson_progress"
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Achievements.IncrementProgressByType(input.UserID, input.Progress, input.AchievementType)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"message": "progress updated"}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}

func (app *application) updateAchievementProgressHandlerLegacy(w http.ResponseWriter, r *http.Request) {
	var input struct {
		UserID        int `json:"user_id"`
		AchievementID int `json:"achievement_id"`
		Progress      int `json:"progress"`
	}

	err := app.readJSON(w, r, &input)
	if err != nil {
		app.badRequestResponse(w, r, err)
		return
	}

	err = app.models.Achievements.UpdateAchievementProgress(input.UserID, input.AchievementID, input.Progress)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}

	err = app.writeJSON(w, http.StatusOK, envelope{"message": "achievement updated"}, nil)
	if err != nil {
		app.serverErrorResponse(w, r, err)
		return
	}
}
