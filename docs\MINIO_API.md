# MinIO File Storage API

Этот документ описывает API для работы с файлами (аудио и изображения) через MinIO в проекте Kazakh-Lingo.

## Конфигурация

MinIO настроен в `docker-compose.yaml` со следующими параметрами:
- **Endpoint**: `minio:9000` (внутри Docker сети)
- **Access Key**: `olzzhas`
- **Secret Key**: `Olzhas040404`
- **Console**: доступен на порту `9001`
- **API**: доступен на порту `9000`

## Buckets

Система автоматически создает два bucket'а:
- `klingo-audio` - для аудио файлов
- `klingo-images` - для изображений

## API Endpoints

### 1. Загрузка аудио файла

**POST** `/v1/files/upload/audio`

Загружает аудио файл в MinIO.

**Параметры:**
- `audio` (file) - аудио файл для загрузки

**Поддерживаемые форматы:**
- MP3 (.mp3)
- WAV (.wav)
- OGG (.ogg)
- M4A (.m4a)

**Максимальный размер:** 10MB

**Пример запроса:**
```bash
curl -X POST \
  http://localhost:8080/v1/files/upload/audio \
  -H 'Content-Type: multipart/form-data' \
  -F 'audio=@/path/to/audio.mp3'
```

**Пример ответа:**
```json
{
  "message": "Audio file uploaded successfully",
  "file_url": "http://minio:9000/klingo-audio/uuid-filename.mp3"
}
```

### 2. Загрузка изображения

**POST** `/v1/files/upload/image`

Загружает изображение в MinIO.

**Параметры:**
- `image` (file) - изображение для загрузки

**Поддерживаемые форматы:**
- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)

**Максимальный размер:** 5MB

**Пример запроса:**
```bash
curl -X POST \
  http://localhost:8080/v1/files/upload/image \
  -H 'Content-Type: multipart/form-data' \
  -F 'image=@/path/to/image.jpg'
```

**Пример ответа:**
```json
{
  "message": "Image file uploaded successfully",
  "file_url": "http://minio:9000/klingo-images/uuid-filename.jpg"
}
```

### 3. Множественная загрузка файлов

**POST** `/v1/files/upload/multiple`

Загружает несколько файлов одновременно.

**Параметры:**
- `files` (files) - массив файлов для загрузки

**Максимальный размер:** 50MB (общий)

**Пример запроса:**
```bash
curl -X POST \
  http://localhost:8080/v1/files/upload/multiple \
  -H 'Content-Type: multipart/form-data' \
  -F 'files=@/path/to/audio.mp3' \
  -F 'files=@/path/to/image.jpg'
```

**Пример ответа:**
```json
{
  "message": "Multiple file upload completed",
  "uploaded_files": [
    {
      "filename": "audio.mp3",
      "file_url": "http://minio:9000/klingo-audio/uuid-audio.mp3",
      "type": "audio"
    },
    {
      "filename": "image.jpg",
      "file_url": "http://minio:9000/klingo-images/uuid-image.jpg",
      "type": "image"
    }
  ],
  "uploaded_count": 2,
  "total_count": 2
}
```

### 4. Удаление файла

**DELETE** `/v1/files/delete`

Удаляет файл из MinIO.

**Параметры (JSON):**
```json
{
  "file_url": "http://minio:9000/klingo-audio/uuid-filename.mp3"
}
```

**Пример запроса:**
```bash
curl -X DELETE \
  http://localhost:8080/v1/files/delete \
  -H 'Content-Type: application/json' \
  -d '{"file_url": "http://minio:9000/klingo-audio/uuid-filename.mp3"}'
```

**Пример ответа:**
```json
{
  "message": "File deleted successfully"
}
```

### 5. Список файлов

**GET** `/v1/files/list?bucket=klingo-audio&prefix=`

Возвращает список файлов в указанном bucket.

**Параметры:**
- `bucket` (required) - имя bucket'а (`klingo-audio` или `klingo-images`)
- `prefix` (optional) - префикс для фильтрации файлов

**Пример запроса:**
```bash
curl -X GET \
  "http://localhost:8080/v1/files/list?bucket=klingo-audio&prefix="
```

**Пример ответа:**
```json
{
  "bucket": "klingo-audio",
  "prefix": "",
  "files": [
    "uuid1-audio1.mp3",
    "uuid2-audio2.wav"
  ],
  "count": 2
}
```

## Доступ к MinIO Console

MinIO Console доступен по адресу: `http://localhost:9001`

**Учетные данные:**
- Username: `olzzhas`
- Password: `Olzhas040404`

## Интеграция с существующими моделями

### Обновление модели Word

Для интеграции с существующей моделью `Word`, можно обновить поле `audio_url`:

```go
// При создании слова с аудио
word := &data.Word{
    KazPlaintext: "Сәлем",
    RusPlaintext: "Привет",
    AudioURL:     "http://minio:9000/klingo-audio/uuid-audio.mp3", // URL из MinIO
}
```

### Обновление модели User

Для профильных изображений:

```go
// При обновлении профиля пользователя
user.ImageUrl = "http://minio:9000/klingo-images/uuid-profile.jpg"
```

## Безопасность

1. **Публичный доступ**: Файлы в bucket'ах настроены для публичного чтения
2. **Аутентификация**: API endpoints защищены системой аутентификации приложения
3. **Валидация**: Проверка типов файлов и размеров на уровне API

## Мониторинг

MinIO предоставляет метрики через:
- Console UI на порту 9001
- Health check endpoint: `http://localhost:9000/minio/health/live`

## Резервное копирование

Данные MinIO хранятся в Docker volume `minio_data`. Для резервного копирования:

```bash
# Создание backup
docker run --rm -v kazakh-lingo_minio_data:/data -v $(pwd):/backup alpine tar czf /backup/minio-backup.tar.gz /data

# Восстановление backup
docker run --rm -v kazakh-lingo_minio_data:/data -v $(pwd):/backup alpine tar xzf /backup/minio-backup.tar.gz -C /
```
