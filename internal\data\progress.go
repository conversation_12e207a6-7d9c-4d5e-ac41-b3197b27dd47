package data

import (
	"context"
	"database/sql"
	"github.com/go-redis/redis/v8"
	"github.com/lib/pq"
	"time"
)

type Progress struct {
	ID                  int    `json:"id"`
	UserID              int    `json:"user_id"`
	ModuleID            int    `json:"module_id"`
	MistakenQuestionIds []int  `json:"mistaken_question_ids"`
	Time                string `json:"time"`
	TryCount            int    `json:"try_count"`
	CreatedAt           string `json:"created_at"`
	UpdatedAt           string `json:"updated_at"`
}

type ProgressModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

func (p *ProgressModel) SaveProgress(progress *Progress) (*Progress, error) {
	queryCheck := `
		SELECT COALESCE(MAX(try_count), 0) + 1 
		FROM progress 
		WHERE user_id = $1 AND module_id = $2
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	var tryCount int
	err := p.DB.QueryRowContext(ctx, queryCheck, progress.UserID, progress.ModuleID).Scan(&tryCount)
	if err != nil {
		return nil, err
	}

	progress.TryCount = tryCount

	queryInsert := `
		INSERT INTO progress (user_id, module_id, mistaken_question_ids, time, try_count)
		VALUES ($1, $2, $3, $4, $5)
		RETURNING id, created_at, updated_at
	`

	args := []interface{}{&progress.UserID, &progress.ModuleID, pq.Array(progress.MistakenQuestionIds), &progress.Time, &progress.TryCount}

	err = p.DB.QueryRowContext(ctx, queryInsert, args...).Scan(&progress.ID, &progress.CreatedAt, &progress.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return progress, nil
}

func (p *ProgressModel) GetStreak(userID int64) (map[string]int, error) {
	query := `
		SELECT created_at
		FROM progress
		WHERE user_id = $1
		ORDER BY created_at ASC
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := p.DB.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var dates []time.Time
	for rows.Next() {
		var createdAt time.Time
		err := rows.Scan(&createdAt)
		if err != nil {
			return nil, err
		}
		dates = append(dates, createdAt)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	currentStreak, maxStreak := calculateStreaks(dates)

	return map[string]int{
		"current_streak": currentStreak,
		"max_streak":     maxStreak,
	}, nil
}

func calculateStreaks(dates []time.Time) (int, int) {
	if len(dates) == 0 {
		return 0, 0
	}

	currentStreak := 0
	maxStreak := 0
	tempStreak := 1

	now := time.Now().Truncate(24 * time.Hour)

	for i := 1; i < len(dates); i++ {
		prevDate := dates[i-1].Truncate(24 * time.Hour)
		currDate := dates[i].Truncate(24 * time.Hour)

		if currDate.Sub(prevDate).Hours() <= 24 {
			tempStreak++
		} else {
			if tempStreak > maxStreak {
				maxStreak = tempStreak
			}
			tempStreak = 1
		}
	}

	if tempStreak > maxStreak {
		maxStreak = tempStreak
	}

	lastActivity := dates[len(dates)-1].Truncate(24 * time.Hour)
	if now.Sub(lastActivity).Hours() <= 24 {
		currentStreak = tempStreak
	}

	return currentStreak, maxStreak
}
