<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinIO File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .result {
            background-color: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>MinIO File Upload Test - Kazakh Lingo</h1>
    
    <div class="upload-section">
        <h2>Upload Audio File</h2>
        <input type="file" id="audioFile" accept=".mp3,.wav,.ogg,.m4a">
        <button onclick="uploadAudio()">Upload Audio</button>
        <div id="audioResult" class="result"></div>
    </div>

    <div class="upload-section">
        <h2>Upload Image File</h2>
        <input type="file" id="imageFile" accept=".jpg,.jpeg,.png,.gif,.webp">
        <button onclick="uploadImage()">Upload Image</button>
        <div id="imageResult" class="result"></div>
    </div>

    <div class="upload-section">
        <h2>Upload Multiple Files</h2>
        <input type="file" id="multipleFiles" multiple accept=".mp3,.wav,.ogg,.m4a,.jpg,.jpeg,.png,.gif,.webp">
        <button onclick="uploadMultiple()">Upload Multiple</button>
        <div id="multipleResult" class="result"></div>
    </div>

    <div class="upload-section">
        <h2>List Files</h2>
        <select id="bucketSelect">
            <option value="klingo-audio">Audio Files (klingo-audio)</option>
            <option value="klingo-images">Image Files (klingo-images)</option>
        </select>
        <button onclick="listFiles()">List Files</button>
        <div id="listResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/v1';

        async function uploadAudio() {
            const fileInput = document.getElementById('audioFile');
            const resultDiv = document.getElementById('audioResult');
            
            if (!fileInput.files[0]) {
                resultDiv.textContent = 'Please select an audio file';
                return;
            }

            const formData = new FormData();
            formData.append('audio', fileInput.files[0]);

            try {
                resultDiv.textContent = 'Uploading...';
                const response = await fetch(`${API_BASE}/files/upload/audio`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const resultDiv = document.getElementById('imageResult');
            
            if (!fileInput.files[0]) {
                resultDiv.textContent = 'Please select an image file';
                return;
            }

            const formData = new FormData();
            formData.append('image', fileInput.files[0]);

            try {
                resultDiv.textContent = 'Uploading...';
                const response = await fetch(`${API_BASE}/files/upload/image`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function uploadMultiple() {
            const fileInput = document.getElementById('multipleFiles');
            const resultDiv = document.getElementById('multipleResult');
            
            if (!fileInput.files.length) {
                resultDiv.textContent = 'Please select files';
                return;
            }

            const formData = new FormData();
            for (let file of fileInput.files) {
                formData.append('files', file);
            }

            try {
                resultDiv.textContent = 'Uploading...';
                const response = await fetch(`${API_BASE}/files/upload/multiple`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }

        async function listFiles() {
            const bucketSelect = document.getElementById('bucketSelect');
            const resultDiv = document.getElementById('listResult');
            
            const bucket = bucketSelect.value;

            try {
                resultDiv.textContent = 'Loading...';
                const response = await fetch(`${API_BASE}/files/list?bucket=${bucket}`);
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
