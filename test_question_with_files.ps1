# Test Question Creation with Files

Write-Host "Testing Question Creation with Files API..." -ForegroundColor Green

# Create test files
$testAudioContent = "This is a test audio file for question"
$testAudioPath = "test_question_audio.mp3"
[System.IO.File]::WriteAllText($testAudioPath, $testAudioContent)

$testImageContent = "This is a test image file for question"
$testImagePath = "test_question_image.jpg"
[System.IO.File]::WriteAllText($testImagePath, $testImageContent)

Write-Host "Created test files: $testAudioPath, $testImagePath" -ForegroundColor Yellow

# Prepare question data
$questionData = @{
    type = "multiple-choice"
    correct_answer = @{
        kaz_plaintext = "Мектеп"
        rus_plaintext = "Школа"
        sequence = @(1, 2, 3)
    }
} | ConvertTo-Json -Depth 3

Write-Host "Question data:" -ForegroundColor Yellow
Write-Host $questionData -ForegroundColor Cyan

# Test with curl (if available)
Write-Host "`nTesting question creation with files..." -ForegroundColor Yellow
try {
    $curlResult = & curl.exe -X POST `
        -F "question_data=$questionData" `
        -F "audio=@$testAudioPath" `
        -F "image=@$testImagePath" `
        http://localhost:8080/v1/questions 2>&1
    
    Write-Host "✅ Question with files created successfully!" -ForegroundColor Green
    Write-Host $curlResult -ForegroundColor Cyan
} catch {
    Write-Host "❌ Curl failed: $($_.Exception.Message)" -ForegroundColor Red
    
    # Alternative method using PowerShell
    Write-Host "Trying alternative PowerShell method..." -ForegroundColor Yellow
    try {
        $boundary = [System.Guid]::NewGuid().ToString()
        $LF = "`r`n"
        
        # Read files
        $audioBytes = [System.IO.File]::ReadAllBytes($testAudioPath)
        $audioEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($audioBytes)
        
        $imageBytes = [System.IO.File]::ReadAllBytes($testImagePath)
        $imageEnc = [System.Text.Encoding]::GetEncoding('iso-8859-1').GetString($imageBytes)
        
        # Build multipart body
        $bodyLines = (
            "--$boundary",
            "Content-Disposition: form-data; name=`"question_data`"$LF",
            $questionData,
            "--$boundary",
            "Content-Disposition: form-data; name=`"audio`"; filename=`"$testAudioPath`"",
            "Content-Type: audio/mpeg$LF",
            $audioEnc,
            "--$boundary",
            "Content-Disposition: form-data; name=`"image`"; filename=`"$testImagePath`"",
            "Content-Type: image/jpeg$LF",
            $imageEnc,
            "--$boundary--$LF"
        ) -join $LF
        
        $response = Invoke-RestMethod -Uri "http://localhost:8080/v1/questions" -Method Post -ContentType "multipart/form-data; boundary=$boundary" -Body $bodyLines
        Write-Host "✅ Question with files created successfully!" -ForegroundColor Green
        Write-Host ($response | ConvertTo-Json -Depth 4) -ForegroundColor Cyan
    } catch {
        Write-Host "❌ Alternative method failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response body: $responseBody" -ForegroundColor Red
        }
    }
}

# Test list questions
Write-Host "`nTesting list questions..." -ForegroundColor Yellow
try {
    $listResponse = Invoke-RestMethod -Uri "http://localhost:8080/v1/questions" -Method GET
    Write-Host "✅ List questions successful!" -ForegroundColor Green
    Write-Host ($listResponse | ConvertTo-Json -Depth 4) -ForegroundColor Cyan
} catch {
    Write-Host "❌ List questions failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Cleanup
Write-Host "`nCleaning up test files..." -ForegroundColor Yellow
Remove-Item $testAudioPath -ErrorAction SilentlyContinue
Remove-Item $testImagePath -ErrorAction SilentlyContinue
Write-Host "Test completed!" -ForegroundColor Green
