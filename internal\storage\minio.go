package storage

import (
	"context"
	"fmt"
	"log"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

type MinIOStorage struct {
	Client   *minio.Client
	Endpoint string
	UseSSL   bool
}

type FileType string

const (
	FileTypeAudio FileType = "audio"
	FileTypeImage FileType = "image"
)

// NewMinIOStorage создает новый экземпляр MinIO storage
func NewMinIOStorage(endpoint, accessKey, secretKey string, useSSL bool) (*MinIOStorage, error) {
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKey, secretKey, ""),
		Secure: useSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %v", err)
	}

	storage := &MinIOStorage{
		Client:   client,
		Endpoint: endpoint,
		UseSSL:   useSSL,
	}

	// Создаем необходимые buckets при инициализации
	if err := storage.createBucketsIfNotExist(); err != nil {
		return nil, fmt.Errorf("failed to create buckets: %v", err)
	}

	return storage, nil
}

// createBucketsIfNotExist создает необходимые buckets если они не существуют
func (s *MinIOStorage) createBucketsIfNotExist() error {
	ctx := context.Background()
	buckets := []string{"klingo-audio", "klingo-images"}

	for _, bucketName := range buckets {
		exists, err := s.Client.BucketExists(ctx, bucketName)
		if err != nil {
			return fmt.Errorf("error checking bucket %s: %v", bucketName, err)
		}

		if !exists {
			err = s.Client.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
			if err != nil {
				return fmt.Errorf("error creating bucket %s: %v", bucketName, err)
			}
			log.Printf("Created bucket: %s", bucketName)

			// Устанавливаем политику для публичного чтения
			policy := fmt.Sprintf(`{
				"Version": "2012-10-17",
				"Statement": [
					{
						"Effect": "Allow",
						"Principal": {"AWS": ["*"]},
						"Action": ["s3:GetObject"],
						"Resource": ["arn:aws:s3:::%s/*"]
					}
				]
			}`, bucketName)

			err = s.Client.SetBucketPolicy(ctx, bucketName, policy)
			if err != nil {
				log.Printf("Warning: Could not set public policy for bucket %s: %v", bucketName, err)
			}
		}
	}

	return nil
}

// UploadFile загружает файл в MinIO
func (s *MinIOStorage) UploadFile(ctx context.Context, file multipart.File, header *multipart.FileHeader, fileType FileType) (string, error) {
	// Определяем bucket в зависимости от типа файла
	var bucketName string
	switch fileType {
	case FileTypeAudio:
		bucketName = "klingo-audio"
	case FileTypeImage:
		bucketName = "klingo-images"
	default:
		return "", fmt.Errorf("unsupported file type: %s", fileType)
	}

	// Генерируем уникальное имя файла
	ext := filepath.Ext(header.Filename)
	fileName := fmt.Sprintf("%s%s", uuid.New().String(), ext)

	// Определяем content type
	contentType := header.Header.Get("Content-Type")
	if contentType == "" {
		contentType = getContentType(ext)
	}

	// Загружаем файл
	_, err := s.Client.PutObject(ctx, bucketName, fileName, file, header.Size, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload file: %v", err)
	}

	// Формируем URL для доступа к файлу
	protocol := "http"
	if s.UseSSL {
		protocol = "https"
	}

	fileURL := fmt.Sprintf("%s://%s/%s/%s", protocol, s.Endpoint, bucketName, fileName)
	return fileURL, nil
}

// UploadAudio загружает аудио файл
func (s *MinIOStorage) UploadAudio(ctx context.Context, file multipart.File, header *multipart.FileHeader) (string, error) {
	return s.UploadFile(ctx, file, header, FileTypeAudio)
}

// UploadImage загружает изображение
func (s *MinIOStorage) UploadImage(ctx context.Context, file multipart.File, header *multipart.FileHeader) (string, error) {
	return s.UploadFile(ctx, file, header, FileTypeImage)
}

// DeleteFile удаляет файл из MinIO
func (s *MinIOStorage) DeleteFile(ctx context.Context, fileURL string) error {
	// Извлекаем bucket и object name из URL
	bucketName, objectName, err := s.parseFileURL(fileURL)
	if err != nil {
		return err
	}

	err = s.Client.RemoveObject(ctx, bucketName, objectName, minio.RemoveObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	return nil
}

// parseFileURL извлекает bucket и object name из URL
func (s *MinIOStorage) parseFileURL(fileURL string) (string, string, error) {
	// Ожидаем URL вида: http://endpoint/bucket/object
	parts := strings.Split(fileURL, "/")
	if len(parts) < 5 {
		return "", "", fmt.Errorf("invalid file URL format")
	}

	bucketName := parts[3]
	objectName := strings.Join(parts[4:], "/")

	return bucketName, objectName, nil
}

// GetFileURL возвращает presigned URL для файла (для приватных файлов)
func (s *MinIOStorage) GetFileURL(ctx context.Context, bucketName, objectName string, expiry time.Duration) (string, error) {
	url, err := s.Client.PresignedGetObject(ctx, bucketName, objectName, expiry, nil)
	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %v", err)
	}

	return url.String(), nil
}

// getContentType определяет content type по расширению файла
func getContentType(ext string) string {
	ext = strings.ToLower(ext)
	switch ext {
	case ".mp3":
		return "audio/mpeg"
	case ".wav":
		return "audio/wav"
	case ".ogg":
		return "audio/ogg"
	case ".m4a":
		return "audio/mp4"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".webp":
		return "image/webp"
	default:
		return "application/octet-stream"
	}
}

// ListFiles возвращает список файлов в bucket
func (s *MinIOStorage) ListFiles(ctx context.Context, bucketName string, prefix string) ([]string, error) {
	var files []string

	objectCh := s.Client.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	for object := range objectCh {
		if object.Err != nil {
			return nil, object.Err
		}
		files = append(files, object.Key)
	}

	return files, nil
}

// CopyFile копирует файл из одного места в другое
func (s *MinIOStorage) CopyFile(ctx context.Context, srcBucket, srcObject, destBucket, destObject string) error {
	src := minio.CopySrcOptions{
		Bucket: srcBucket,
		Object: srcObject,
	}

	dest := minio.CopyDestOptions{
		Bucket: destBucket,
		Object: destObject,
	}

	_, err := s.Client.CopyObject(ctx, dest, src)
	if err != nil {
		return fmt.Errorf("failed to copy file: %v", err)
	}

	return nil
}

// GetFileInfo возвращает информацию о файле
func (s *MinIOStorage) GetFileInfo(ctx context.Context, bucketName, objectName string) (*minio.ObjectInfo, error) {
	info, err := s.Client.StatObject(ctx, bucketName, objectName, minio.StatObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %v", err)
	}

	return &info, nil
}
