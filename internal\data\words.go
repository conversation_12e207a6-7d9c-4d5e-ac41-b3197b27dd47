package data

import (
	"database/sql"
	"github.com/go-redis/redis/v8"
)

type Word struct {
	ID           int    `json:"id"`
	KazPlaintext string `json:"kaz_plaintext"`
	RusPlaintext string `json:"rus_plaintext"`
	AudioURL     string `json:"audio_url"`
}

type WordModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

// Insert Создание слова
func (m *WordModel) Insert(word *Word) error {
	query := `
		INSERT INTO words (kaz_plaintext, rus_plaintext, audio_url)
		VALUES ($1, $2, $3)
		RETURNING id
	`
	args := []any{word.KazPlaintext, word.RusPlaintext, word.AudioURL}

	return m.DB.QueryRow(query, args...).Scan(&word.ID)
}

// GetAll Получение всех слов
func (m *WordModel) GetAll() ([]Word, error) {
	query := `SELECT id, kaz_plaintext, rus_plaintext, audio_url FROM words`
	rows, err := m.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var words []Word
	for rows.Next() {
		var word Word
		err := rows.Scan(&word.ID, &word.KazPlaintext, &word.RusPlaintext, &word.AudioURL)
		if err != nil {
			return nil, err
		}
		words = append(words, word)
	}

	return words, nil
}
