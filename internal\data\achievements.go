package data

import (
	"context"
	"database/sql"
	"github.com/go-redis/redis/v8"
	"time"
)

type Achievement struct {
	ID          int       `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	Target      int       `json:"target"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type UserAchievement struct {
	AchievementID int  `json:"achievement_id"`
	UserID        int  `json:"user_id"`
	Progress      int  `json:"progress"`
	Achieved      bool `json:"achieved"`
}

type AchievementModel struct {
	DB    *sql.DB
	Redis *redis.Client
}

// IncrementProgressByType обновляет прогресс для всех достижений указанного типа
func (m *AchievementModel) IncrementProgressByType(userID int, progress int, achievementType string) error {
	query := `
		INSERT INTO user_achievements (user_id, achievement_id, progress, achieved)
		SELECT 
		    $1 AS user_id, 
		    id AS achievement_id, 
		    $2 AS progress, 
		    CASE WHEN $2 >= target THEN TRUE ELSE FALSE END AS achieved
		FROM achievements
		WHERE type = $3
		ON CONFLICT (user_id, achievement_id) DO UPDATE
		SET 
		    progress = user_achievements.progress + EXCLUDED.progress,
		    achieved = CASE WHEN user_achievements.progress + EXCLUDED.progress >= (SELECT target FROM achievements WHERE id = user_achievements.achievement_id) THEN TRUE ELSE FALSE END,
		    updated_at = NOW()
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, userID, progress, achievementType)
	return err
}

// ResetStreakProgressIfBroken сбрасывает прогресс достижений типа "streaks", если пользователь пропустил день
func (m *AchievementModel) ResetStreakProgressIfBroken(userID int) error {
	query := `
		UPDATE user_achievements
		SET progress = 0, achieved = FALSE
		WHERE user_id = $1 
		  AND achievement_id IN (
		    SELECT id FROM achievements WHERE type = 'streaks'
		  )
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, userID)
	return err
}

// UnlockAchievement открывает достижение, если условие выполнено
func (m *AchievementModel) UnlockAchievement(userID int, achievementKey string) error {
	query := `
		INSERT INTO user_achievements (user_id, achievement_id, progress, achieved)
		SELECT $1, id, target, TRUE
		FROM achievements
		WHERE key = $2
		ON CONFLICT (user_id, achievement_id) DO NOTHING
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, userID, achievementKey)
	return err
}

// GetUserAchievements Получение достижений пользователя
func (m *AchievementModel) GetUserAchievements(userID int64) ([]UserAchievement, error) {
	query := `
		SELECT
			achievements.id,
			achievements.name,
			achievements.description,
			achievements.type,
			achievements.target,
			COALESCE(user_achievements.progress, 0) AS progress,
			COALESCE(user_achievements.achieved, false) AS achieved
		FROM achievements
		LEFT JOIN user_achievements
		ON achievements.id = user_achievements.achievement_id
		WHERE user_achievements.user_id = $1
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := m.DB.QueryContext(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var achievements []UserAchievement
	for rows.Next() {
		var achievement UserAchievement
		err := rows.Scan(
			&achievement.AchievementID,
			&achievement.UserID,
			&achievement.Progress,
			&achievement.Achieved,
		)
		if err != nil {
			return nil, err
		}
		achievements = append(achievements, achievement)
	}

	return achievements, nil
}

func (m *AchievementModel) GetAllAchievements() ([]Achievement, error) {
	query := `
		SELECT id, name, description, type, target, created_at, updated_at
		FROM achievements
		ORDER BY type, target
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	rows, err := m.DB.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}

	defer rows.Close()

	var achievements []Achievement
	for rows.Next() {
		var achievement Achievement
		err := rows.Scan(
			&achievement.ID,
			&achievement.Name,
			&achievement.Description,
			&achievement.Type,
			&achievement.Target,
			&achievement.CreatedAt,
			&achievement.UpdatedAt,
		)
		if err != nil {
			return nil, err
		}
		achievements = append(achievements, achievement)
	}

	return achievements, nil
}

// UpdateAchievementProgress Обновление прогресса
func (m *AchievementModel) UpdateAchievementProgress(userID, achievementID, progress int) error {
	query := `
		INSERT INTO user_achievements (user_id, achievement_id, progress, achieved)
		VALUES ($1, $2, $3, CASE WHEN $3 >= (SELECT target FROM achievements WHERE id = $2) THEN TRUE ELSE FALSE END)
		ON CONFLICT (user_id, achievement_id) DO UPDATE
		SET progress = user_achievements.progress + $3,
		    achieved = CASE WHEN user_achievements.progress + $3 >= (SELECT target FROM achievements WHERE id = $2) THEN TRUE ELSE FALSE END,
		    updated_at = NOW()
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := m.DB.ExecContext(ctx, query, userID, achievementID, progress)
	return err
}

// SaveAchievement Сохранение нового достижения
func (m *AchievementModel) SaveAchievement(achievement *Achievement) (*Achievement, error) {
	query := `
		INSERT INTO achievements (name, description, type, target)
		VALUES ($1, $2, $3, $4)
		RETURNING id, created_at, updated_at
	`

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := m.DB.QueryRowContext(ctx, query,
		achievement.Name,
		achievement.Description,
		achievement.Type,
		achievement.Target,
	).Scan(&achievement.ID, &achievement.CreatedAt, &achievement.UpdatedAt)
	if err != nil {
		return nil, err
	}

	return achievement, nil
}
